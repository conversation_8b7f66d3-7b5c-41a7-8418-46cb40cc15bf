"use client";

import React from "react";
import { <PERSON>, Users, Target, Heart, TrendingUp, Zap } from "lucide-react";
import { GlowingEffect } from "@/components/ui/glowing-effect";
import { BlurFade } from "../ui/blur-fade";

export function GlowingEffectDemo() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
      <GridItem
        icon={<Rocket className="h-6 w-6 text-white" />}
        title="Innovation"
        description="We push boundaries and embrace cutting-edge technologies to create solutions that transform industries."
      />

      <GridItem
        icon={<Users className="h-6 w-6 text-white" />}
        title="Collaboration"
        description="We believe great ideas emerge from teamwork, open communication, and diverse perspectives working together."
      />

      <GridItem
        icon={<Target className="h-6 w-6 text-white" />}
        title="Excellence"
        description="We commit to delivering high-quality products that exceed expectations and stand the test of time."
      />

      <GridItem
        icon={<Heart className="h-6 w-6 text-white" />}
        title="User-Centered"
        description="We design with empathy, putting users at the center of everything we create to solve real problems."
      />

      <GridItem
        icon={<Zap className="h-6 w-6 text-white" />}
        title="Adaptability"
        description="We embrace change, continuously learn, and pivot quickly to stay ahead in a rapidly evolving landscape."
      />

      <GridItem
        icon={<TrendingUp className="h-6 w-6 text-white" />}
        title="Impact"
        description="We measure success by the positive difference our solutions make in people's lives and businesses."
      />
    </div>
  );
}

interface GridItemProps {
  icon: React.ReactNode;
  title: string;
  description: React.ReactNode;
}

const GridItem = ({ icon, title, description }: GridItemProps) => {
  return (
    <div className="min-h-[14rem] group">
      <div className="relative h-full rounded-2xl border border-white/10 p-3 hover:border-white/30 transition-all duration-300 overflow-hidden">
        <GlowingEffect
          spread={120}
          glow={true}
          disabled={false}
        />
        <div className="relative z-20 flex h-full flex-col gap-4 p-6 bg-black/40 backdrop-blur-sm rounded-xl border border-white/5">
          <div className="w-fit rounded-lg border border-white/30 p-3 bg-white/10 backdrop-blur-sm">
            {icon}
          </div>
          <div className="space-y-3 flex-1">
            <h3 className="font-manrope text-xl md:text-2xl font-semibold text-white">
              {title}
            </h3>
            <p className="font-dm-sans text-sm md:text-base text-white/80 leading-relaxed">
              {description}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

const Grid = () => {
  return (
    <section className="relative overflow-hidden bg-black">
      <BlurFade direction="up" delay={0.2} offset={30} inViewMargin="-10%">
        <div className="container mx-auto px-4">
          <GlowingEffectDemo />
        </div>
      </BlurFade>
    </section>
  );
};

export default Grid;
